# 照片上传下载系统

基于Spring Boot开发的企业级照片上传下载系统，提供完整的文件管理、安全控制和性能优化功能。

## 🚀 功能特性

### 核心功能
- ✅ **单文件/多文件上传**：支持同时上传多个图片文件
- ✅ **文件下载**：支持断点续传和批量下载
- ✅ **在线预览**：支持图片在线预览和缩略图生成
- ✅ **文件管理**：完整的CRUD操作，支持批量删除

### 安全特性
- 🔒 **文件类型验证**：使用Apache Tika进行严格的文件类型检测
- 🔒 **文件大小限制**：可配置的文件大小限制（默认10MB）
- 🔒 **防盗链保护**：基于Referer的防盗链机制
- 🔒 **访问权限控制**：基于Spring Security的用户认证和授权
- 🔒 **XSS防护**：文件名安全处理，防止XSS攻击
- 🔒 **路径遍历防护**：防止目录遍历攻击

### 性能优化
- ⚡ **图片压缩**：自动压缩大尺寸图片，节省存储空间
- ⚡ **缩略图生成**：自动生成缩略图，提升加载速度
- ⚡ **缓存机制**：使用Spring Cache缓存缩略图
- ⚡ **文件去重**：基于MD5的文件去重机制
- ⚡ **异步处理**：支持异步文件处理

### 监控与管理
- 📊 **存储统计**：实时统计文件数量、大小等信息
- 📊 **访问统计**：记录文件访问次数和时间
- 📊 **定时清理**：自动清理过期和未使用的文件
- 📊 **健康检查**：集成Spring Boot Actuator

## 🛠️ 技术栈

- **框架**：Spring Boot 3.2.0
- **安全**：Spring Security
- **数据库**：H2 Database (可切换为MySQL/PostgreSQL)
- **ORM**：Spring Data JPA
- **图片处理**：Thumbnailator
- **文件检测**：Apache Tika
- **API文档**：SpringDoc OpenAPI 3
- **测试**：JUnit 5 + Mockito
- **构建工具**：Maven

## 📋 系统要求

- Java 17+
- Maven 3.6+
- 内存：至少512MB
- 磁盘：根据存储需求

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd photo-upload-system
```

### 2. 编译运行
```bash
mvn clean package
java -jar target/photo-upload-system-0.0.1-SNAPSHOT.jar
```

### 3. 访问系统
- **Web界面**：http://localhost:8080
- **API文档**：http://localhost:8080/swagger-ui.html
- **H2控制台**：http://localhost:8080/h2-console

### 4. 默认账户
- **管理员**：admin / admin123
- **普通用户**：user / user123

## 📖 API文档

### 文件上传
```http
POST /api/photos/upload/single
Content-Type: multipart/form-data

file: <image-file>
```

```http
POST /api/photos/upload/multiple
Content-Type: multipart/form-data

files: <image-files>
```

### 文件下载
```http
GET /api/photos/download/{storedName}
```

### 文件预览
```http
GET /api/photos/preview/{storedName}
```

### 获取缩略图
```http
GET /api/photos/thumbnail/{storedName}
```

### 照片列表
```http
GET /api/photos/list?page=0&size=20
```

### 删除照片
```http
DELETE /api/photos/{id}
```

## ⚙️ 配置说明

### 文件上传配置
```yaml
file:
  upload:
    path: ./uploads/                    # 文件存储路径
    max-size: 10485760                  # 最大文件大小(10MB)
    allowed-types:                      # 允许的文件类型
      - image/jpeg
      - image/png
      - image/gif
    compression:
      enabled: true                     # 启用压缩
      quality: 0.85                     # 压缩质量
      max-width: 1920                   # 最大宽度
      max-height: 1080                  # 最大高度
    thumbnail:
      width: 200                        # 缩略图宽度
      height: 200                       # 缩略图高度
      quality: 0.8                      # 缩略图质量
```

### 安全配置
```yaml
security:
  referer:
    enabled: true                       # 启用防盗链
    allowed-domains:                    # 允许的域名
      - localhost
      - 127.0.0.1
```

## 🧪 测试

### 运行单元测试
```bash
mvn test
```

### 运行集成测试
```bash
mvn verify
```

### 测试覆盖率
```bash
mvn jacoco:report
```

## 📁 项目结构

```
src/
├── main/
│   ├── java/com/example/photoupload/
│   │   ├── config/              # 配置类
│   │   ├── controller/          # 控制器
│   │   ├── dto/                 # 数据传输对象
│   │   ├── entity/              # 实体类
│   │   ├── exception/           # 异常处理
│   │   ├── interceptor/         # 拦截器
│   │   ├── repository/          # 数据访问层
│   │   ├── service/             # 业务逻辑层
│   │   ├── task/                # 定时任务
│   │   └── util/                # 工具类
│   └── resources/
│       ├── static/              # 静态资源
│       └── application.yml      # 配置文件
└── test/                        # 测试代码
```
## 核心流程详解

本章节详细描述了系统的核心工作流程，包括照片的上传和下载/预览，以帮助开发者更深入地理解系统内部的工作原理。

### 照片上传流程

照片上传是系统的核心功能，其处理流程经过精心设计，以确保安全性、性能和数据的完整性。

1.  **请求入口 (Controller Layer)**
    *   客户端通过 `multipart/form-data` 格式向 `/api/photos/upload/single` 或 `/api/photos/upload/multiple` 端点发起 `POST` 请求。
    *   `PhotoController` 接收到请求，Spring MVC 自动将上传的文件封装为 `MultipartFile` 对象。
    *   控制器从 `Authentication` 对象中获取当前认证的用户名，然后调用 `PhotoService` 来处理业务逻辑。

2.  **业务处理 (Service Layer)**
    *   `PhotoServiceImpl` 是上传功能的核心，它执行以下一系列操作：
    *   **a. 文件验证**:
        *   **空文件检查**: 确保上传的文件不为空。
        *   **文件类型验证**: 使用 `FileUtil` 和 `Apache Tika` 库检测文件的真实MIME类型，并与 `application.yml` 中配置的 `file.upload.allowed-types` 列表进行比对，拒绝不合法的类型。
        *   **文件大小检查**: 检查文件大小是否超过 `file.upload.max-size` 限制。
    *   **b. 文件去重 (MD5 Check)**:
        *   计算上传文件的 MD5 哈希值。
        *   查询 `PhotoInfoRepository`，检查数据库中是否已存在相同哈希值的记录。如果存在，则直接返回现有文件的信息，避免重复存储，节省空间。
    *   **c. 文件处理与存储**:
        *   **生成存储路径**: 根据当前日期创建子目录（如 `uploads/20250711/`），以优化文件系统的性能。
        *   **生成唯一文件名**: 使用 `日期_UUID_原始文件名` 的格式创建唯一的存储文件名，防止命名冲突并保留原始文件名信息。
        *   **图片压缩 (可选)**: 如果在 `application.yml` 中启用了 `file.upload.compression.enabled`，系统会使用 `Thumbnailator` 库将图片按配置（`max-width`, `max-height`, `quality`）进行等比压缩，以减小文件体积。
        *   **保存原始文件**: 将处理后（可能已压缩）的文件保存到服务器的文件系统中。
    *   **d. 生成缩略图**:
        *   使用 `Thumbnailator` 为上传的图片生成一个标准尺寸的缩略图（尺寸和质量由 `file.upload.thumbnail` 配置定义）。
        *   缩略图被保存在一个专门的子目录中（如 `uploads/20250711/thumbnails/`），以便快速访问。
    *   **e. 数据持久化**:
        *   创建一个 `PhotoInfo` 实体对象，包含原始文件名、存储路径、文件大小、MIME类型、MD5哈希值、上传者用户名等所有元数据。
        *   调用 `photoInfoRepository.save()` 方法将该实体持久化到 H2 数据库中。

3.  **响应 (Response)**
    *   `PhotoService` 将新创建的 `PhotoInfo` 实体映射为 `PhotoUploadResponse` DTO。
    *   该 DTO 包含客户端需要的所有关键信息，如文件ID、存储名、访问URL、缩略图URL等。
    *   `PhotoController` 将 DTO 封装在 `ApiResponse` 对象中，并以 JSON 格式返回给客户端，状态码为 `200 OK`。

### 照片下载/预览流程

照片的下载和预览共享相似的后端逻辑，但通过不同的 HTTP 头来指示浏览器的行为（是下载文件还是直接显示）。

1.  **请求入口与安全检查**
    *   客户端向 `/api/photos/download/{storedName}` (下载) 或 `/api/photos/preview/{storedName}` (预览) 端点发起 `GET` 请求。
    *   **防盗链拦截**: 在请求到达控制器之前，`RefererInterceptor` 会被触发。它会检查请求头中的 `Referer` 是否来自 `security.referer.allowed-domains` 中配置的允许域。如果检查失败，请求将被拒绝，有效防止资源被盗链。

2.  **控制器处理 (Controller Layer)**
    *   `PhotoController` 接收请求，并从路径变量中获取 `storedName`。
    *   它调用 `PhotoService` 来加载对应的文件资源。

3.  **业务处理 (Service Layer)**
    *   `PhotoServiceImpl` 执行以下操作：
    *   **a. 文件定位**:
        *   服务层首先查询数据库 (`PhotoInfoRepository`)，根据 `storedName` 找到对应的 `PhotoInfo` 记录，以确认文件存在并获取其存储路径。
        *   使用 `FileUtil` 将文件系统中的文件加载为 Spring 的 `Resource` 对象。
    *   **b. 缩略图缓存**:
        *   如果请求的是缩略图 (`/api/photos/thumbnail/{storedName}`), `PhotoService` 的 `getThumbnail` 方法会被调用。
        *   该方法被 `@Cacheable("thumbnails")` 注解标记。当首次请求时，它会从文件系统加载缩略图；同时，Spring Cache 会将结果缓存起来。
        *   后续对同一缩略图的请求将直接从缓存中获取，极大提升了响应速度，降低了 I/O 开销。

4.  **响应 (Response)**
    *   `PhotoController` 根据请求的类型（下载、预览或缩略图）构建 `ResponseEntity`。
    *   **设置 `Content-Type`**: 从数据库中获取文件的MIME类型，并设置到响应头中，告知浏览器如何处理内容。
    *   **设置 `Content-Disposition`**:
        *   对于**下载**请求，`Content-Disposition` 头被设置为 `attachment; filename="original_filename.jpg"`。这会提示浏览器弹出文件下载对话框。
        *   对于**预览**请求，不设置此头，浏览器会默认在页面内联显示图片。
    *   **设置缓存头**: 对于预览和缩略图，会设置 `Cache-Control` 头（如 `max-age=3600`），指示浏览器和CDN可以缓存该资源一段时间，减少不必要的后端请求。
    *   最后，将文件资源 (`Resource`) 作为响应体返回给客户端。

## 🔧 部署指南

### Docker部署
```dockerfile
FROM openjdk:17-jre-slim
COPY target/photo-upload-system-0.0.1-SNAPSHOT.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 生产环境配置
1. 修改数据库配置为MySQL/PostgreSQL
2. 配置文件存储路径
3. 设置安全密钥
4. 配置日志级别
5. 启用HTTPS

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🐛 问题反馈

如果您发现任何问题或有改进建议，请在 [Issues](../../issues) 页面提交。

## 📞 联系方式

- 项目维护者：System
- 邮箱：<EMAIL>

## 🙏 致谢

感谢以下开源项目的支持：
- Spring Boot
- Thumbnailator
- Apache Tika
- H2 Database
