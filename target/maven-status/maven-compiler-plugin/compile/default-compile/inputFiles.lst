/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/src/main/java/com/example/photoupload/entity/PhotoInfo.java
/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/src/main/java/com/example/photoupload/exception/GlobalExceptionHandler.java
/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/src/main/java/com/example/photoupload/dto/PageResponse.java
/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/src/main/java/com/example/photoupload/util/ImageUtil.java
/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/src/main/java/com/example/photoupload/config/SecurityConfig.java
/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/src/main/java/com/example/photoupload/interceptor/RefererInterceptor.java
/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/src/main/java/com/example/photoupload/util/FileUtil.java
/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/src/main/java/com/example/photoupload/config/WebSecurityConfig.java
/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/src/main/java/com/example/photoupload/PhotoUploadSystemApplication.java
/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/src/main/java/com/example/photoupload/service/impl/PhotoServiceImpl.java
/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/src/main/java/com/example/photoupload/task/FileCleanupTask.java
/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/src/main/java/com/example/photoupload/exception/FileValidationException.java
/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/src/main/java/com/example/photoupload/service/PhotoService.java
/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/src/main/java/com/example/photoupload/repository/PhotoInfoRepository.java
/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/src/main/java/com/example/photoupload/config/FileUploadConfig.java
/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/src/main/java/com/example/photoupload/controller/PhotoController.java
/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/src/main/java/com/example/photoupload/exception/FileProcessingException.java
/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/src/main/java/com/example/photoupload/config/WebConfig.java
/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/src/main/java/com/example/photoupload/dto/ApiResponse.java
/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/src/main/java/com/example/photoupload/dto/PhotoUploadResponse.java
/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/src/main/java/com/example/photoupload/dto/PhotoListResponse.java
