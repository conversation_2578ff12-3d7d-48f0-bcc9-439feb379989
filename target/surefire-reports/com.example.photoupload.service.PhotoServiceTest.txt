-------------------------------------------------------------------------------
Test set: com.example.photoupload.service.PhotoServiceTest
-------------------------------------------------------------------------------
Tests run: 12, Failures: 3, Errors: 8, Skipped: 0, Time elapsed: 0.289 s <<< FAILURE! -- in com.example.photoupload.service.PhotoServiceTest
com.example.photoupload.service.PhotoServiceTest.testGetStorageStats -- Time elapsed: 0.007 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "org.springframework.data.domain.Page.getContent()" because the return value of "com.example.photoupload.repository.PhotoInfoRepository.findByIsDeletedFalseOrderByUploadTimeDesc(org.springframework.data.domain.Pageable)" is null
	at com.example.photoupload.service.impl.PhotoServiceImpl.getStorageStats(PhotoServiceImpl.java:448)
	at com.example.photoupload.service.PhotoServiceTest.testGetStorageStats(PhotoServiceTest.java:261)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.photoupload.service.PhotoServiceTest.testUploadSingle_EmptyFile -- Time elapsed: 0.006 s <<< FAILURE!
org.opentest4j.AssertionFailedError: Unexpected exception type thrown, expected: <com.example.photoupload.exception.FileValidationException> but was: <com.example.photoupload.exception.FileProcessingException>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:67)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:35)
	at org.junit.jupiter.api.Assertions.assertThrows(Assertions.java:3115)
	at com.example.photoupload.service.PhotoServiceTest.testUploadSingle_EmptyFile(PhotoServiceTest.java:140)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: com.example.photoupload.exception.FileProcessingException: 文件上传失败: 文件不能为空
	at com.example.photoupload.service.impl.PhotoServiceImpl.uploadSingle(PhotoServiceImpl.java:109)
	at com.example.photoupload.service.PhotoServiceTest.lambda$testUploadSingle_EmptyFile$0(PhotoServiceTest.java:141)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:53)
	... 6 more

com.example.photoupload.service.PhotoServiceTest.testDeletePhoto_AdminCanDeleteAny -- Time elapsed: 0.007 s <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:75)
  2. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:76)
  3. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:77)
  4. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:82)
  5. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:87)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.
	at org.mockito.junit.jupiter.MockitoExtension.afterEach(MockitoExtension.java:192)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.photoupload.service.PhotoServiceTest.testDeletePhoto_NoPermission -- Time elapsed: 0.004 s <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:75)
  2. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:76)
  3. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:77)
  4. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:82)
  5. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:87)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.
	at org.mockito.junit.jupiter.MockitoExtension.afterEach(MockitoExtension.java:192)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.photoupload.service.PhotoServiceTest.testFileExists_True -- Time elapsed: 0.003 s <<< ERROR!
java.lang.NullPointerException
	at java.base/java.util.Objects.requireNonNull(Objects.java:233)
	at java.base/sun.nio.fs.UnixFileSystem.getPath(UnixFileSystem.java:296)
	at java.base/java.nio.file.Path.of(Path.java:148)
	at java.base/java.nio.file.Paths.get(Paths.java:69)
	at com.example.photoupload.service.impl.PhotoServiceImpl.fileExists(PhotoServiceImpl.java:388)
	at com.example.photoupload.service.PhotoServiceTest.testFileExists_True(PhotoServiceTest.java:235)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.photoupload.service.PhotoServiceTest.testUploadSingle_FileSizeExceeded -- Time elapsed: 0.004 s <<< FAILURE!
org.opentest4j.AssertionFailedError: Unexpected exception type thrown, expected: <com.example.photoupload.exception.FileValidationException> but was: <com.example.photoupload.exception.FileProcessingException>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:67)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:35)
	at org.junit.jupiter.api.Assertions.assertThrows(Assertions.java:3115)
	at com.example.photoupload.service.PhotoServiceTest.testUploadSingle_FileSizeExceeded(PhotoServiceTest.java:152)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: com.example.photoupload.exception.FileProcessingException: 文件上传失败: 文件大小超过限制: 10485760 bytes
	at com.example.photoupload.service.impl.PhotoServiceImpl.uploadSingle(PhotoServiceImpl.java:109)
	at com.example.photoupload.service.PhotoServiceTest.lambda$testUploadSingle_FileSizeExceeded$1(PhotoServiceTest.java:153)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:53)
	... 6 more

com.example.photoupload.service.PhotoServiceTest.testDeletePhoto_Success -- Time elapsed: 0.002 s <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:75)
  2. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:76)
  3. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:77)
  4. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:82)
  5. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:87)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.
	at org.mockito.junit.jupiter.MockitoExtension.afterEach(MockitoExtension.java:192)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.photoupload.service.PhotoServiceTest.testUploadSingle_FileAlreadyExists -- Time elapsed: 0.002 s <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:75)
  2. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:82)
  3. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:87)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.
	at org.mockito.junit.jupiter.MockitoExtension.afterEach(MockitoExtension.java:192)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.photoupload.service.PhotoServiceTest.testUploadSingle_Success -- Time elapsed: 0.010 s <<< ERROR!
com.example.photoupload.exception.FileProcessingException: 文件上传失败: ./uploads/20231201_12345678_test.jpg
	at com.example.photoupload.service.impl.PhotoServiceImpl.uploadSingle(PhotoServiceImpl.java:109)
	at com.example.photoupload.service.PhotoServiceTest.testUploadSingle_Success(PhotoServiceTest.java:101)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.photoupload.service.PhotoServiceTest.testUploadSingle_InvalidFileType -- Time elapsed: 0.006 s <<< FAILURE!
org.opentest4j.AssertionFailedError: Unexpected exception type thrown, expected: <com.example.photoupload.exception.FileValidationException> but was: <com.example.photoupload.exception.FileProcessingException>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:67)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:35)
	at org.junit.jupiter.api.Assertions.assertThrows(Assertions.java:3115)
	at com.example.photoupload.service.PhotoServiceTest.testUploadSingle_InvalidFileType(PhotoServiceTest.java:163)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: com.example.photoupload.exception.FileProcessingException: 文件上传失败: 不支持的文件类型: text/plain
	at com.example.photoupload.service.impl.PhotoServiceImpl.uploadSingle(PhotoServiceImpl.java:109)
	at com.example.photoupload.service.PhotoServiceTest.lambda$testUploadSingle_InvalidFileType$2(PhotoServiceTest.java:164)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:53)
	... 6 more

com.example.photoupload.service.PhotoServiceTest.testGetContentType -- Time elapsed: 0.003 s <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:75)
  2. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:76)
  3. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:77)
  4. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:82)
  5. -> at com.example.photoupload.service.PhotoServiceTest.setUp(PhotoServiceTest.java:87)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.
	at org.mockito.junit.jupiter.MockitoExtension.afterEach(MockitoExtension.java:192)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

