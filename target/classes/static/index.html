<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>照片上传系统</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .upload-section {
            border: 2px dashed #ddd;
            padding: 30px;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 30px;
            transition: border-color 0.3s;
        }
        
        .upload-section:hover {
            border-color: #007bff;
        }
        
        .file-input {
            margin: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
            display: none;
        }
        
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        
        .result.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .result.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .photo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .photo-item {
            border: 1px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .photo-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }
        
        .photo-info {
            padding: 10px;
            font-size: 12px;
            color: #666;
        }
        
        .photo-actions {
            padding: 10px;
            text-align: center;
        }
        
        .btn-small {
            padding: 5px 10px;
            font-size: 12px;
            margin: 2px;
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            text-align: center;
            margin: 20px 0;
        }
        
        .stat-item {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            flex: 1;
            margin: 0 5px;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        
        .login-section {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .auth-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📸 照片上传系统</h1>
        
        <div class="auth-info">
            <p>默认用户名/密码：admin/admin123 或 user/user123</p>
            <p>当前状态：<span id="authStatus">未登录</span></p>
        </div>
        
        <div class="upload-section">
            <h3>📤 上传照片</h3>
            <p>支持 JPG、PNG、GIF、BMP、WebP 格式，单文件最大 10MB</p>
            
            <div>
                <input type="file" id="singleFile" class="file-input" accept="image/*">
                <button class="btn" onclick="uploadSingle()">上传单个文件</button>
            </div>
            
            <div>
                <input type="file" id="multipleFiles" class="file-input" accept="image/*" multiple>
                <button class="btn" onclick="uploadMultiple()">批量上传</button>
            </div>
            
            <div class="progress" id="uploadProgress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            
            <div class="result" id="uploadResult"></div>
        </div>
    </div>
    
    <div class="container">
        <h3>📊 系统统计</h3>
        <div class="stats" id="stats">
            <div class="stat-item">
                <div class="stat-number" id="totalFiles">-</div>
                <div>总文件数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="totalSize">-</div>
                <div>总大小</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="totalUsers">-</div>
                <div>用户数</div>
            </div>
        </div>
        
        <div style="text-align: center;">
            <button class="btn" onclick="loadStats()">刷新统计</button>
            <button class="btn" onclick="loadPhotos()">加载照片列表</button>
        </div>
    </div>
    
    <div class="container">
        <h3>🖼️ 照片列表</h3>
        <div class="photo-grid" id="photoGrid"></div>
    </div>

    <script>
        // 检查认证状态
        function checkAuthStatus() {
            fetch('/api/photos/stats', {
                credentials: 'include'
            })
            .then(response => {
                if (response.ok) {
                    document.getElementById('authStatus').textContent = '已登录';
                    document.getElementById('authStatus').style.color = 'green';
                } else {
                    document.getElementById('authStatus').textContent = '未登录';
                    document.getElementById('authStatus').style.color = 'red';
                }
            })
            .catch(() => {
                document.getElementById('authStatus').textContent = '未登录';
                document.getElementById('authStatus').style.color = 'red';
            });
        }
        
        // 上传单个文件
        function uploadSingle() {
            const fileInput = document.getElementById('singleFile');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('请选择文件', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', file);
            
            uploadFile('/api/photos/upload/single', formData);
        }
        
        // 批量上传
        function uploadMultiple() {
            const fileInput = document.getElementById('multipleFiles');
            const files = fileInput.files;
            
            if (files.length === 0) {
                showResult('请选择文件', 'error');
                return;
            }
            
            const formData = new FormData();
            for (let i = 0; i < files.length; i++) {
                formData.append('files', files[i]);
            }
            
            uploadFile('/api/photos/upload/multiple', formData);
        }
        
        // 执行文件上传
        function uploadFile(url, formData) {
            const progressDiv = document.getElementById('uploadProgress');
            const progressBar = document.getElementById('progressBar');
            const resultDiv = document.getElementById('uploadResult');
            
            progressDiv.style.display = 'block';
            progressBar.style.width = '0%';
            resultDiv.style.display = 'none';
            
            // 模拟进度
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 30;
                if (progress > 90) progress = 90;
                progressBar.style.width = progress + '%';
            }, 200);
            
            fetch(url, {
                method: 'POST',
                body: formData,
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                clearInterval(progressInterval);
                progressBar.style.width = '100%';
                
                setTimeout(() => {
                    progressDiv.style.display = 'none';
                    if (data.code === 200) {
                        showResult('上传成功！', 'success');
                        loadPhotos(); // 重新加载照片列表
                        loadStats(); // 更新统计
                    } else {
                        showResult('上传失败：' + data.message, 'error');
                    }
                }, 500);
            })
            .catch(error => {
                clearInterval(progressInterval);
                progressDiv.style.display = 'none';
                showResult('上传失败：' + error.message, 'error');
            });
        }
        
        // 显示结果
        function showResult(message, type) {
            const resultDiv = document.getElementById('uploadResult');
            resultDiv.textContent = message;
            resultDiv.className = 'result ' + type;
            resultDiv.style.display = 'block';
            
            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 5000);
        }
        
        // 加载统计信息
        function loadStats() {
            fetch('/api/photos/stats', {
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    document.getElementById('totalFiles').textContent = data.data.totalFiles;
                    document.getElementById('totalSize').textContent = formatFileSize(data.data.totalSize);
                    document.getElementById('totalUsers').textContent = data.data.totalUsers;
                }
            })
            .catch(error => {
                console.error('加载统计失败:', error);
            });
        }
        
        // 加载照片列表
        function loadPhotos() {
            fetch('/api/photos/list?page=0&size=12', {
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    displayPhotos(data.data.content);
                }
            })
            .catch(error => {
                console.error('加载照片列表失败:', error);
            });
        }
        
        // 显示照片
        function displayPhotos(photos) {
            const grid = document.getElementById('photoGrid');
            grid.innerHTML = '';
            
            photos.forEach(photo => {
                const photoDiv = document.createElement('div');
                photoDiv.className = 'photo-item';
                
                photoDiv.innerHTML = `
                    <img src="${photo.thumbnailUrl || photo.previewUrl}" alt="${photo.originalName}" 
                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuaXoOazleWKoOi9vTwvdGV4dD48L3N2Zz4='">
                    <div class="photo-info">
                        <div><strong>${photo.originalName}</strong></div>
                        <div>大小: ${formatFileSize(photo.fileSize)}</div>
                        <div>尺寸: ${photo.width}x${photo.height}</div>
                        <div>上传: ${photo.uploadTime}</div>
                        <div>访问: ${photo.accessCount}次</div>
                    </div>
                    <div class="photo-actions">
                        <button class="btn btn-small" onclick="previewPhoto('${photo.storedName}')">预览</button>
                        <button class="btn btn-small" onclick="downloadPhoto('${photo.storedName}', '${photo.originalName}')">下载</button>
                        <button class="btn btn-small" onclick="deletePhoto(${photo.id})">删除</button>
                    </div>
                `;
                
                grid.appendChild(photoDiv);
            });
        }
        
        // 预览照片
        function previewPhoto(storedName) {
            window.open(`/api/photos/preview/${storedName}`, '_blank');
        }
        
        // 下载照片
        function downloadPhoto(storedName, originalName) {
            const link = document.createElement('a');
            link.href = `/api/photos/download/${storedName}`;
            link.download = originalName;
            link.click();
        }
        
        // 删除照片
        function deletePhoto(id) {
            if (!confirm('确定要删除这张照片吗？')) {
                return;
            }
            
            fetch(`/api/photos/${id}`, {
                method: 'DELETE',
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showResult('删除成功', 'success');
                    loadPhotos();
                    loadStats();
                } else {
                    showResult('删除失败：' + data.message, 'error');
                }
            })
            .catch(error => {
                showResult('删除失败：' + error.message, 'error');
            });
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthStatus();
            loadStats();
            loadPhotos();
        });
    </script>
</body>
</html>
