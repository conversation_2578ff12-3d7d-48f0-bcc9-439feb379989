package com.example.photoupload.service;

import com.example.photoupload.config.FileUploadConfig;
import com.example.photoupload.dto.PhotoUploadResponse;
import com.example.photoupload.entity.PhotoInfo;
import com.example.photoupload.exception.FileValidationException;
import com.example.photoupload.repository.PhotoInfoRepository;
import com.example.photoupload.service.impl.PhotoServiceImpl;
import com.example.photoupload.util.FileUtil;
import com.example.photoupload.util.ImageUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 照片服务测试类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
class PhotoServiceTest {

    @Mock
    private PhotoInfoRepository photoInfoRepository;

    @Mock
    private FileUploadConfig fileUploadConfig;

    @Mock
    private FileUtil fileUtil;

    @Mock
    private ImageUtil imageUtil;

    @InjectMocks
    private PhotoServiceImpl photoService;

    private MultipartFile mockFile;
    private PhotoInfo mockPhotoInfo;

    @BeforeEach
    void setUp() {
        // 创建模拟文件
        mockFile = new MockMultipartFile(
                "file",
                "test.jpg",
                "image/jpeg",
                "test image content".getBytes()
        );

        // 创建模拟照片信息
        mockPhotoInfo = new PhotoInfo();
        mockPhotoInfo.setId(1L);
        mockPhotoInfo.setOriginalName("test.jpg");
        mockPhotoInfo.setStoredName("20231201_12345678_test.jpg");
        mockPhotoInfo.setFileSize(1024L);
        mockPhotoInfo.setContentType("image/jpeg");
        mockPhotoInfo.setMd5Hash("abcd1234");
        mockPhotoInfo.setUploadedBy("testuser");

        // 配置模拟对象
        when(fileUploadConfig.getPath()).thenReturn("./uploads/");
        when(fileUploadConfig.getMaxSize()).thenReturn(10485760L);
        when(fileUploadConfig.getAllowedTypes()).thenReturn(Arrays.asList("image/jpeg", "image/png"));
        
        FileUploadConfig.Compression compression = new FileUploadConfig.Compression();
        compression.setEnabled(true);
        compression.setQuality(0.85);
        when(fileUploadConfig.getCompression()).thenReturn(compression);
        
        FileUploadConfig.Thumbnail thumbnail = new FileUploadConfig.Thumbnail();
        thumbnail.setWidth(200);
        thumbnail.setHeight(200);
        when(fileUploadConfig.getThumbnail()).thenReturn(thumbnail);
    }

    @Test
    void testUploadSingle_Success() {
        // 准备测试数据
        when(fileUtil.isValidImageFile(mockFile)).thenReturn(true);
        when(fileUtil.calculateMD5(mockFile)).thenReturn("abcd1234");
        when(fileUtil.generateStoredName(anyString())).thenReturn("20231201_12345678_test.jpg");
        when(photoInfoRepository.findByMd5HashAndIsDeletedFalse(anyString())).thenReturn(Optional.empty());
        when(photoInfoRepository.save(any(PhotoInfo.class))).thenReturn(mockPhotoInfo);
        when(imageUtil.getImageDimensions(mockFile)).thenReturn(new int[]{800, 600});

        // 执行测试
        PhotoUploadResponse response = photoService.uploadSingle(mockFile, "testuser");

        // 验证结果
        assertNotNull(response);
        assertEquals("test.jpg", response.getOriginalName());
        assertEquals("20231201_12345678_test.jpg", response.getStoredName());
        assertEquals(1024L, response.getFileSize());
        assertEquals("image/jpeg", response.getContentType());

        // 验证方法调用
        verify(photoInfoRepository).save(any(PhotoInfo.class));
        verify(fileUtil).calculateMD5(mockFile);
        verify(fileUtil).generateStoredName("test.jpg");
    }

    @Test
    void testUploadSingle_FileAlreadyExists() {
        // 准备测试数据 - 文件已存在
        when(fileUtil.isValidImageFile(mockFile)).thenReturn(true);
        when(fileUtil.calculateMD5(mockFile)).thenReturn("abcd1234");
        when(photoInfoRepository.findByMd5HashAndIsDeletedFalse("abcd1234")).thenReturn(Optional.of(mockPhotoInfo));

        // 执行测试
        PhotoUploadResponse response = photoService.uploadSingle(mockFile, "testuser");

        // 验证结果
        assertNotNull(response);
        assertEquals("test.jpg", response.getOriginalName());

        // 验证不会保存新记录
        verify(photoInfoRepository, never()).save(any(PhotoInfo.class));
    }

    @Test
    void testUploadSingle_EmptyFile() {
        // 创建空文件
        MultipartFile emptyFile = new MockMultipartFile("file", "empty.jpg", "image/jpeg", new byte[0]);

        // 执行测试并验证异常
        assertThrows(FileValidationException.class, () -> {
            photoService.uploadSingle(emptyFile, "testuser");
        });
    }

    @Test
    void testUploadSingle_FileSizeExceeded() {
        // 创建超大文件
        byte[] largeContent = new byte[20 * 1024 * 1024]; // 20MB
        MultipartFile largeFile = new MockMultipartFile("file", "large.jpg", "image/jpeg", largeContent);

        // 执行测试并验证异常
        assertThrows(FileValidationException.class, () -> {
            photoService.uploadSingle(largeFile, "testuser");
        });
    }

    @Test
    void testUploadSingle_InvalidFileType() {
        // 创建不支持的文件类型
        MultipartFile invalidFile = new MockMultipartFile("file", "test.txt", "text/plain", "test content".getBytes());

        // 执行测试并验证异常
        assertThrows(FileValidationException.class, () -> {
            photoService.uploadSingle(invalidFile, "testuser");
        });
    }

    @Test
    void testUploadMultiple_Success() {
        // 准备测试数据
        MultipartFile[] files = {mockFile, mockFile};
        
        when(fileUtil.isValidImageFile(any(MultipartFile.class))).thenReturn(true);
        when(fileUtil.calculateMD5(any(MultipartFile.class))).thenReturn("abcd1234", "efgh5678");
        when(fileUtil.generateStoredName(anyString())).thenReturn("20231201_12345678_test.jpg", "20231201_87654321_test.jpg");
        when(photoInfoRepository.findByMd5HashAndIsDeletedFalse(anyString())).thenReturn(Optional.empty());
        when(photoInfoRepository.save(any(PhotoInfo.class))).thenReturn(mockPhotoInfo);
        when(imageUtil.getImageDimensions(any(MultipartFile.class))).thenReturn(new int[]{800, 600});

        // 执行测试
        var responses = photoService.uploadMultiple(files, "testuser");

        // 验证结果
        assertEquals(2, responses.size());
        verify(photoInfoRepository, times(2)).save(any(PhotoInfo.class));
    }

    @Test
    void testDeletePhoto_Success() {
        // 准备测试数据
        when(photoInfoRepository.findById(1L)).thenReturn(Optional.of(mockPhotoInfo));
        when(photoInfoRepository.softDeleteById(1L)).thenReturn(1);

        // 执行测试
        boolean result = photoService.deletePhoto(1L, "testuser");

        // 验证结果
        assertTrue(result);
        verify(photoInfoRepository).softDeleteById(1L);
    }

    @Test
    void testDeletePhoto_NoPermission() {
        // 准备测试数据 - 不同用户
        when(photoInfoRepository.findById(1L)).thenReturn(Optional.of(mockPhotoInfo));

        // 执行测试
        boolean result = photoService.deletePhoto(1L, "otheruser");

        // 验证结果
        assertFalse(result);
        verify(photoInfoRepository, never()).softDeleteById(anyLong());
    }

    @Test
    void testDeletePhoto_AdminCanDeleteAny() {
        // 准备测试数据 - 管理员用户
        when(photoInfoRepository.findById(1L)).thenReturn(Optional.of(mockPhotoInfo));
        when(photoInfoRepository.softDeleteById(1L)).thenReturn(1);

        // 执行测试
        boolean result = photoService.deletePhoto(1L, "admin");

        // 验证结果
        assertTrue(result);
        verify(photoInfoRepository).softDeleteById(1L);
    }

    @Test
    void testFileExists_True() {
        // 准备测试数据
        when(photoInfoRepository.findByStoredNameAndIsDeletedFalse("test.jpg")).thenReturn(Optional.of(mockPhotoInfo));

        // 执行测试
        boolean exists = photoService.fileExists("test.jpg");

        // 验证结果 - 注意：这个测试可能需要实际文件系统支持
        // 在单元测试中，我们主要测试数据库查询逻辑
        verify(photoInfoRepository).findByStoredNameAndIsDeletedFalse("test.jpg");
    }

    @Test
    void testGetContentType() {
        // 准备测试数据
        when(photoInfoRepository.findByStoredNameAndIsDeletedFalse("test.jpg")).thenReturn(Optional.of(mockPhotoInfo));

        // 执行测试
        String contentType = photoService.getContentType("test.jpg");

        // 验证结果
        assertEquals("image/jpeg", contentType);
    }

    @Test
    void testGetStorageStats() {
        // 准备测试数据
        when(photoInfoRepository.countByIsDeletedFalse()).thenReturn(100L);
        when(photoInfoRepository.sumFileSizeByIsDeletedFalse()).thenReturn(1024000L);

        // 执行测试
        PhotoService.StorageStats stats = photoService.getStorageStats();

        // 验证结果
        assertNotNull(stats);
        assertEquals(100L, stats.getTotalFiles());
        assertEquals(1024000L, stats.getTotalSize());
    }
}
