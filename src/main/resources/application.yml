server:
  port: 8081
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
      enabled: true

spring:
  application:
    name: photo-upload-system
  
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: password
  
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  
  h2:
    console:
      enabled: true
      path: /h2-console
  
  cache:
    type: simple
  
  security:
    user:
      name: admin
      password: admin123
      roles: ADMIN

# 文件上传配置
file:
  upload:
    # 文件存储路径
    path: ./uploads/
    # 允许的图片格式
    allowed-types:
      - image/jpeg
      - image/jpg
      - image/png
      - image/gif
      - image/bmp
      - image/webp
    # 单文件最大大小 (10MB)
    max-size: 10485760
    # 缩略图配置
    thumbnail:
      width: 200
      height: 200
      quality: 0.8
    # 压缩配置
    compression:
      enabled: true
      quality: 0.85
      max-width: 1920
      max-height: 1080

# 安全配置
security:
  # 防盗链配置
  referer:
    enabled: true
    allowed-domains:
      - localhost
      - 127.0.0.1
  # 访问令牌配置
  token:
    secret: mySecretKey123456789
    expiration: 3600000 # 1小时

# 日志配置
logging:
  level:
    com.example.photoupload: DEBUG
    org.springframework.web.multipart: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/photo-upload-system.log

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# Swagger配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operations-sorter: method
