package com.example.photoupload.controller;

import com.example.photoupload.dto.ApiResponse;
import com.example.photoupload.dto.PageResponse;
import com.example.photoupload.dto.PhotoListResponse;
import com.example.photoupload.dto.PhotoUploadResponse;
import com.example.photoupload.service.PhotoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 照片上传下载控制器
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/api/photos")
@Tag(name = "照片管理", description = "照片上传、下载、预览等功能")
public class PhotoController {

    private static final Logger logger = LoggerFactory.getLogger(PhotoController.class);

    @Autowired
    private PhotoService photoService;

    /**
     * 单文件上传
     */
    @PostMapping("/upload/single")
    @Operation(summary = "单文件上传", description = "上传单个图片文件")
    public ResponseEntity<ApiResponse<PhotoUploadResponse>> uploadSingle(
            @Parameter(description = "图片文件", required = true)
            @RequestParam("file") MultipartFile file,
            Authentication authentication) {
        
        logger.info("单文件上传请求，文件名: {}, 用户: {}", 
                file.getOriginalFilename(), authentication.getName());
        
        PhotoUploadResponse response = photoService.uploadSingle(file, authentication.getName());
        return ResponseEntity.ok(ApiResponse.success("文件上传成功", response));
    }

    /**
     * 多文件上传
     */
    @PostMapping("/upload/multiple")
    @Operation(summary = "多文件上传", description = "批量上传多个图片文件")
    public ResponseEntity<ApiResponse<List<PhotoUploadResponse>>> uploadMultiple(
            @Parameter(description = "图片文件列表", required = true)
            @RequestParam("files") MultipartFile[] files,
            Authentication authentication) {
        
        logger.info("多文件上传请求，文件数量: {}, 用户: {}", 
                files.length, authentication.getName());
        
        List<PhotoUploadResponse> responses = photoService.uploadMultiple(files, authentication.getName());
        return ResponseEntity.ok(ApiResponse.success("文件上传完成", responses));
    }

    /**
     * 文件下载
     */
    @GetMapping("/download/{storedName}")
    @Operation(summary = "文件下载", description = "下载指定的图片文件")
    public ResponseEntity<Resource> downloadFile(
            @Parameter(description = "存储文件名", required = true)
            @PathVariable String storedName) {
        
        logger.debug("文件下载请求: {}", storedName);
        
        Resource resource = photoService.downloadFile(storedName);
        String contentType = photoService.getContentType(storedName);
        
        // 获取原始文件名用于下载
        String originalName = getOriginalFileName(storedName);
        String encodedFileName = URLEncoder.encode(originalName, StandardCharsets.UTF_8);
        
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType != null ? contentType : "application/octet-stream"))
                .header(HttpHeaders.CONTENT_DISPOSITION, 
                        "attachment; filename=\"" + encodedFileName + "\"")
                .body(resource);
    }

    /**
     * 文件预览
     */
    @GetMapping("/preview/{storedName}")
    @Operation(summary = "文件预览", description = "在线预览图片文件")
    public ResponseEntity<Resource> previewFile(
            @Parameter(description = "存储文件名", required = true)
            @PathVariable String storedName) {
        
        logger.debug("文件预览请求: {}", storedName);
        
        Resource resource = photoService.previewFile(storedName);
        String contentType = photoService.getContentType(storedName);
        
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType != null ? contentType : "image/jpeg"))
                .header(HttpHeaders.CACHE_CONTROL, "max-age=3600")
                .body(resource);
    }

    /**
     * 获取缩略图
     */
    @GetMapping("/thumbnail/{storedName}")
    @Operation(summary = "获取缩略图", description = "获取图片的缩略图")
    public ResponseEntity<Resource> getThumbnail(
            @Parameter(description = "存储文件名", required = true)
            @PathVariable String storedName) {
        
        logger.debug("缩略图请求: {}", storedName);
        
        Resource resource = photoService.getThumbnail(storedName);
        
        return ResponseEntity.ok()
                .contentType(MediaType.IMAGE_JPEG)
                .header(HttpHeaders.CACHE_CONTROL, "max-age=7200")
                .body(resource);
    }

    /**
     * 获取照片列表（分页）
     */
    @GetMapping("/list")
    @Operation(summary = "获取照片列表", description = "分页获取所有照片列表")
    public ResponseEntity<ApiResponse<PageResponse<PhotoListResponse>>> getPhotoList(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        
        logger.debug("获取照片列表请求，页码: {}, 大小: {}", page, size);
        
        Pageable pageable = PageRequest.of(page, size);
        PageResponse<PhotoListResponse> response = photoService.getPhotoList(pageable);
        
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取用户照片列表（分页）
     */
    @GetMapping("/list/my")
    @Operation(summary = "获取我的照片列表", description = "分页获取当前用户的照片列表")
    public ResponseEntity<ApiResponse<PageResponse<PhotoListResponse>>> getMyPhotoList(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size,
            Authentication authentication) {
        
        logger.debug("获取用户照片列表请求，用户: {}, 页码: {}, 大小: {}", 
                authentication.getName(), page, size);
        
        Pageable pageable = PageRequest.of(page, size);
        PageResponse<PhotoListResponse> response = photoService.getUserPhotoList(
                authentication.getName(), pageable);
        
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取照片详情
     */
    @GetMapping("/detail/{id}")
    @Operation(summary = "获取照片详情", description = "根据ID获取照片详细信息")
    public ResponseEntity<ApiResponse<PhotoListResponse>> getPhotoDetail(
            @Parameter(description = "照片ID", required = true)
            @PathVariable Long id) {
        
        logger.debug("获取照片详情请求: {}", id);
        
        PhotoListResponse response = photoService.getPhotoDetail(id);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 删除照片
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除照片", description = "删除指定的照片")
    public ResponseEntity<ApiResponse<String>> deletePhoto(
            @Parameter(description = "照片ID", required = true)
            @PathVariable Long id,
            Authentication authentication) {

        logger.info("删除照片请求: {}, 用户: {}", id, authentication.getName());

        boolean success = photoService.deletePhoto(id, authentication.getName());
        if (success) {
            return ResponseEntity.ok(ApiResponse.success("照片删除成功"));
        } else {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest("照片删除失败，可能不存在或无权限"));
        }
    }

    /**
     * 批量删除照片
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除照片", description = "批量删除多个照片")
    public ResponseEntity<ApiResponse<String>> batchDeletePhotos(
            @Parameter(description = "照片ID列表", required = true)
            @RequestBody List<Long> ids,
            Authentication authentication) {
        
        logger.info("批量删除照片请求: {}, 用户: {}", ids, authentication.getName());
        
        int deletedCount = photoService.batchDeletePhotos(ids, authentication.getName());
        String message = String.format("批量删除完成，成功删除 %d/%d 张照片", deletedCount, ids.size());

        return ResponseEntity.ok(ApiResponse.success(message));
    }

    /**
     * 获取存储统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取存储统计", description = "获取系统存储统计信息")
    public ResponseEntity<ApiResponse<PhotoService.StorageStats>> getStorageStats() {
        logger.debug("获取存储统计请求");
        
        PhotoService.StorageStats stats = photoService.getStorageStats();
        return ResponseEntity.ok(ApiResponse.success(stats));
    }

    /**
     * 从存储文件名中提取原始文件名（简化实现）
     */
    private String getOriginalFileName(String storedName) {
        // 存储文件名格式：日期_UUID_原始文件名
        // 这里简化处理，实际应该从数据库获取
        int lastUnderscoreIndex = storedName.lastIndexOf('_');
        if (lastUnderscoreIndex != -1 && lastUnderscoreIndex < storedName.length() - 1) {
            return storedName.substring(lastUnderscoreIndex + 1);
        }
        return storedName;
    }
}
