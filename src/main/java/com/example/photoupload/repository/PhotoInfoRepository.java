package com.example.photoupload.repository;

import com.example.photoupload.entity.PhotoInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 照片信息数据访问层
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public interface PhotoInfoRepository extends JpaRepository<PhotoInfo, Long> {

    /**
     * 根据存储文件名查找照片信息
     */
    Optional<PhotoInfo> findByStoredNameAndIsDeletedFalse(String storedName);

    /**
     * 根据MD5值查找照片信息（用于去重）
     */
    Optional<PhotoInfo> findByMd5HashAndIsDeletedFalse(String md5Hash);

    /**
     * 根据上传用户查找照片列表
     */
    Page<PhotoInfo> findByUploadedByAndIsDeletedFalseOrderByUploadTimeDesc(String uploadedBy, Pageable pageable);

    /**
     * 查找所有未删除的照片
     */
    Page<PhotoInfo> findByIsDeletedFalseOrderByUploadTimeDesc(Pageable pageable);

    /**
     * 根据内容类型查找照片
     */
    List<PhotoInfo> findByContentTypeAndIsDeletedFalse(String contentType);

    /**
     * 查找指定时间之前上传的照片（用于清理）
     */
    List<PhotoInfo> findByUploadTimeBeforeAndIsDeletedFalse(LocalDateTime dateTime);

    /**
     * 统计用户上传的照片数量
     */
    long countByUploadedByAndIsDeletedFalse(String uploadedBy);

    /**
     * 统计总的照片数量
     */
    long countByIsDeletedFalse();

    /**
     * 计算总的文件大小
     */
    @Query("SELECT SUM(p.fileSize) FROM PhotoInfo p WHERE p.isDeleted = false")
    Long sumFileSizeByIsDeletedFalse();

    /**
     * 软删除照片
     */
    @Modifying
    @Query("UPDATE PhotoInfo p SET p.isDeleted = true WHERE p.id = :id")
    int softDeleteById(@Param("id") Long id);

    /**
     * 批量软删除照片
     */
    @Modifying
    @Query("UPDATE PhotoInfo p SET p.isDeleted = true WHERE p.id IN :ids")
    int softDeleteByIds(@Param("ids") List<Long> ids);

    /**
     * 更新访问统计
     */
    @Modifying
    @Query("UPDATE PhotoInfo p SET p.accessCount = p.accessCount + 1, p.lastAccessTime = :accessTime WHERE p.id = :id")
    int updateAccessStats(@Param("id") Long id, @Param("accessTime") LocalDateTime accessTime);

    /**
     * 查找热门照片（按访问次数排序）
     */
    Page<PhotoInfo> findByIsDeletedFalseOrderByAccessCountDesc(Pageable pageable);

    /**
     * 根据文件大小范围查找照片
     */
    List<PhotoInfo> findByFileSizeBetweenAndIsDeletedFalse(Long minSize, Long maxSize);

    /**
     * 查找需要清理的临时文件
     */
    @Query("SELECT p FROM PhotoInfo p WHERE p.isDeleted = false AND p.lastAccessTime < :cutoffTime AND p.accessCount = 0")
    List<PhotoInfo> findUnusedPhotosBeforeTime(@Param("cutoffTime") LocalDateTime cutoffTime);
}
