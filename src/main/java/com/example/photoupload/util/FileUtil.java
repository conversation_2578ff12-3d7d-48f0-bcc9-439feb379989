package com.example.photoupload.util;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.tika.Tika;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 文件处理工具类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Component
public class FileUtil {

    private static final Logger logger = LoggerFactory.getLogger(FileUtil.class);
    
    /**
     * 支持的图片MIME类型
     */
    private static final List<String> SUPPORTED_IMAGE_TYPES = Arrays.asList(
            "image/jpeg", "image/jpg", "image/png", "image/gif", 
            "image/bmp", "image/webp", "image/tiff"
    );
    
    /**
     * 支持的图片文件扩展名
     */
    private static final List<String> SUPPORTED_IMAGE_EXTENSIONS = Arrays.asList(
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".tiff"
    );
    
    private final Tika tika = new Tika();

    /**
     * 生成存储文件名
     * 格式：日期_UUID_原始文件名
     * 
     * @param originalFilename 原始文件名
     * @return 存储文件名
     */
    public String generateStoredName(String originalFilename) {
        if (StringUtils.isBlank(originalFilename)) {
            throw new IllegalArgumentException("原始文件名不能为空");
        }
        
        // 获取文件扩展名
        String extension = getFileExtension(originalFilename);
        
        // 生成时间戳
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        
        // 生成UUID（取前8位）
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        
        // 清理原始文件名（移除特殊字符）
        String cleanName = sanitizeFileName(getFileNameWithoutExtension(originalFilename));
        
        return String.format("%s_%s_%s%s", timestamp, uuid, cleanName, extension);
    }

    /**
     * 计算文件MD5值
     * 
     * @param file 文件
     * @return MD5值
     */
    public String calculateMD5(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            return DigestUtils.md5Hex(inputStream);
        } catch (IOException e) {
            logger.error("计算MD5失败: {}", e.getMessage(), e);
            throw new RuntimeException("计算MD5失败", e);
        }
    }

    /**
     * 验证是否为有效的图片文件
     * 使用Apache Tika进行文件类型检测
     * 
     * @param file 文件
     * @return 是否为有效图片
     */
    public boolean isValidImageFile(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            String detectedType = tika.detect(inputStream);
            logger.debug("检测到的文件类型: {}, 声明的类型: {}", detectedType, file.getContentType());
            
            // 检查检测到的类型是否为支持的图片类型
            boolean isValidType = SUPPORTED_IMAGE_TYPES.contains(detectedType);
            
            // 检查文件扩展名
            String extension = getFileExtension(file.getOriginalFilename()).toLowerCase();
            boolean hasValidExtension = SUPPORTED_IMAGE_EXTENSIONS.contains(extension);
            
            return isValidType && hasValidExtension;
            
        } catch (IOException e) {
            logger.error("文件类型检测失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取文件扩展名
     * 
     * @param filename 文件名
     * @return 扩展名（包含点号）
     */
    public String getFileExtension(String filename) {
        if (StringUtils.isBlank(filename)) {
            return "";
        }
        
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }
        
        return filename.substring(lastDotIndex);
    }

    /**
     * 获取不包含扩展名的文件名
     * 
     * @param filename 文件名
     * @return 不包含扩展名的文件名
     */
    public String getFileNameWithoutExtension(String filename) {
        if (StringUtils.isBlank(filename)) {
            return "";
        }
        
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return filename;
        }
        
        return filename.substring(0, lastDotIndex);
    }

    /**
     * 清理文件名，移除特殊字符
     * 
     * @param filename 文件名
     * @return 清理后的文件名
     */
    public String sanitizeFileName(String filename) {
        if (StringUtils.isBlank(filename)) {
            return "unnamed";
        }
        
        // 移除或替换特殊字符
        String sanitized = filename.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5._-]", "_");
        
        // 限制长度
        if (sanitized.length() > 50) {
            sanitized = sanitized.substring(0, 50);
        }
        
        // 确保不为空
        if (StringUtils.isBlank(sanitized)) {
            sanitized = "unnamed";
        }
        
        return sanitized;
    }

    /**
     * 格式化文件大小
     * 
     * @param size 文件大小（字节）
     * @return 格式化后的大小字符串
     */
    public String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 检查文件名是否安全
     * 防止路径遍历攻击
     * 
     * @param filename 文件名
     * @return 是否安全
     */
    public boolean isSecureFileName(String filename) {
        if (StringUtils.isBlank(filename)) {
            return false;
        }
        
        // 检查是否包含路径遍历字符
        if (filename.contains("..") || filename.contains("/") || filename.contains("\\")) {
            return false;
        }
        
        // 检查是否为系统保留名称（Windows）
        String[] reservedNames = {"CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4", 
                                  "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2", 
                                  "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"};
        
        String nameWithoutExt = getFileNameWithoutExtension(filename).toUpperCase();
        for (String reserved : reservedNames) {
            if (reserved.equals(nameWithoutExt)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 获取MIME类型对应的文件扩展名
     * 
     * @param mimeType MIME类型
     * @return 文件扩展名
     */
    public String getExtensionFromMimeType(String mimeType) {
        if (StringUtils.isBlank(mimeType)) {
            return ".bin";
        }
        
        return switch (mimeType.toLowerCase()) {
            case "image/jpeg", "image/jpg" -> ".jpg";
            case "image/png" -> ".png";
            case "image/gif" -> ".gif";
            case "image/bmp" -> ".bmp";
            case "image/webp" -> ".webp";
            case "image/tiff" -> ".tiff";
            default -> ".bin";
        };
    }
}
