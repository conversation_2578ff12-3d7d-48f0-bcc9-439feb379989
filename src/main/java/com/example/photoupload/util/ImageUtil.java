package com.example.photoupload.util;

import net.coobird.thumbnailator.Thumbnails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 图片处理工具类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Component
public class ImageUtil {

    private static final Logger logger = LoggerFactory.getLogger(ImageUtil.class);

    /**
     * 获取图片尺寸
     * 
     * @param file 图片文件
     * @return 尺寸数组 [宽度, 高度]，如果获取失败返回null
     */
    public int[] getImageDimensions(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            BufferedImage image = ImageIO.read(inputStream);
            if (image != null) {
                return new int[]{image.getWidth(), image.getHeight()};
            }
        } catch (IOException e) {
            logger.error("获取图片尺寸失败: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取图片尺寸（从文件路径）
     * 
     * @param filePath 文件路径
     * @return 尺寸数组 [宽度, 高度]，如果获取失败返回null
     */
    public int[] getImageDimensions(Path filePath) {
        try {
            BufferedImage image = ImageIO.read(filePath.toFile());
            if (image != null) {
                return new int[]{image.getWidth(), image.getHeight()};
            }
        } catch (IOException e) {
            logger.error("获取图片尺寸失败: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 压缩图片
     * 
     * @param filePath 文件路径
     * @param quality 压缩质量 (0.0-1.0)
     * @param maxWidth 最大宽度
     * @param maxHeight 最大高度
     * @return 是否压缩成功
     */
    public boolean compressImage(Path filePath, double quality, int maxWidth, int maxHeight) {
        try {
            // 获取原始图片尺寸
            int[] dimensions = getImageDimensions(filePath);
            if (dimensions == null) {
                logger.warn("无法获取图片尺寸，跳过压缩: {}", filePath);
                return false;
            }
            
            int originalWidth = dimensions[0];
            int originalHeight = dimensions[1];
            
            // 检查是否需要压缩
            if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
                logger.debug("图片尺寸已符合要求，跳过压缩: {}x{}", originalWidth, originalHeight);
                return false;
            }
            
            // 计算压缩后的尺寸（保持宽高比）
            double widthRatio = (double) maxWidth / originalWidth;
            double heightRatio = (double) maxHeight / originalHeight;
            double ratio = Math.min(widthRatio, heightRatio);
            
            int newWidth = (int) (originalWidth * ratio);
            int newHeight = (int) (originalHeight * ratio);
            
            // 执行压缩
            Thumbnails.of(filePath.toFile())
                    .size(newWidth, newHeight)
                    .outputQuality(quality)
                    .toFile(filePath.toFile());
            
            logger.info("图片压缩成功: {} -> {}x{}", filePath, newWidth, newHeight);
            return true;
            
        } catch (IOException e) {
            logger.error("图片压缩失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 生成缩略图
     * 
     * @param originalPath 原始图片路径
     * @param width 缩略图宽度
     * @param height 缩略图高度
     * @param quality 质量
     * @return 缩略图路径，如果生成失败返回null
     */
    public String generateThumbnail(Path originalPath, int width, int height, double quality) {
        try {
            // 生成缩略图文件名
            String originalFileName = originalPath.getFileName().toString();
            String extension = getFileExtension(originalFileName);
            String nameWithoutExt = getFileNameWithoutExtension(originalFileName);
            String thumbnailFileName = nameWithoutExt + "_thumb" + extension;
            
            Path thumbnailPath = originalPath.getParent().resolve(thumbnailFileName);
            
            // 生成缩略图
            Thumbnails.of(originalPath.toFile())
                    .size(width, height)
                    .outputQuality(quality)
                    .toFile(thumbnailPath.toFile());
            
            logger.info("缩略图生成成功: {}", thumbnailPath);
            return thumbnailPath.toString();
            
        } catch (IOException e) {
            logger.error("生成缩略图失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 生成方形缩略图（裁剪模式）
     * 
     * @param originalPath 原始图片路径
     * @param size 缩略图尺寸（正方形）
     * @param quality 质量
     * @return 缩略图路径，如果生成失败返回null
     */
    public String generateSquareThumbnail(Path originalPath, int size, double quality) {
        try {
            String originalFileName = originalPath.getFileName().toString();
            String extension = getFileExtension(originalFileName);
            String nameWithoutExt = getFileNameWithoutExtension(originalFileName);
            String thumbnailFileName = nameWithoutExt + "_square_thumb" + extension;
            
            Path thumbnailPath = originalPath.getParent().resolve(thumbnailFileName);
            
            // 生成方形缩略图（居中裁剪）
            Thumbnails.of(originalPath.toFile())
                    .size(size, size)
                    .outputQuality(quality)
                    .toFile(thumbnailPath.toFile());
            
            logger.info("方形缩略图生成成功: {}", thumbnailPath);
            return thumbnailPath.toString();
            
        } catch (IOException e) {
            logger.error("生成方形缩略图失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 添加水印
     * 
     * @param originalPath 原始图片路径
     * @param watermarkText 水印文字
     * @param opacity 透明度 (0.0-1.0)
     * @return 是否添加成功
     */
    public boolean addWatermark(Path originalPath, String watermarkText, float opacity) {
        try {
            // 这里可以使用Thumbnailator或其他库添加水印
            // 简化实现，实际项目中可以根据需要扩展
            logger.info("水印功能待实现: {}", watermarkText);
            return true;
            
        } catch (Exception e) {
            logger.error("添加水印失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 旋转图片
     * 
     * @param filePath 文件路径
     * @param angle 旋转角度（度）
     * @return 是否旋转成功
     */
    public boolean rotateImage(Path filePath, double angle) {
        try {
            Thumbnails.of(filePath.toFile())
                    .scale(1.0)
                    .rotate(angle)
                    .toFile(filePath.toFile());
            
            logger.info("图片旋转成功: {} 度", angle);
            return true;
            
        } catch (IOException e) {
            logger.error("图片旋转失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查是否为有效的图片格式
     * 
     * @param file 文件
     * @return 是否为有效图片
     */
    public boolean isValidImage(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            BufferedImage image = ImageIO.read(inputStream);
            return image != null;
        } catch (IOException e) {
            logger.debug("图片格式验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }
        return filename.substring(lastDotIndex);
    }

    /**
     * 获取不包含扩展名的文件名
     */
    private String getFileNameWithoutExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return filename;
        }
        return filename.substring(0, lastDotIndex);
    }

    /**
     * 计算合适的缩略图尺寸（保持宽高比）
     * 
     * @param originalWidth 原始宽度
     * @param originalHeight 原始高度
     * @param maxWidth 最大宽度
     * @param maxHeight 最大高度
     * @return 新尺寸数组 [宽度, 高度]
     */
    public int[] calculateThumbnailSize(int originalWidth, int originalHeight, int maxWidth, int maxHeight) {
        double widthRatio = (double) maxWidth / originalWidth;
        double heightRatio = (double) maxHeight / originalHeight;
        double ratio = Math.min(widthRatio, heightRatio);
        
        int newWidth = (int) (originalWidth * ratio);
        int newHeight = (int) (originalHeight * ratio);
        
        return new int[]{newWidth, newHeight};
    }
}
