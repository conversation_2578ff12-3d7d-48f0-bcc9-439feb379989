package com.example.photoupload.task;

import com.example.photoupload.service.PhotoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 文件清理定时任务
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Component
public class FileCleanupTask {

    private static final Logger logger = LoggerFactory.getLogger(FileCleanupTask.class);

    @Autowired
    private PhotoService photoService;

    /**
     * 清理过期文件
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredFiles() {
        logger.info("开始执行文件清理任务");
        
        try {
            // 清理30天前的未使用文件
            int cleanedCount = photoService.cleanupExpiredFiles(30);
            logger.info("文件清理任务完成，清理文件数量: {}", cleanedCount);
            
        } catch (Exception e) {
            logger.error("文件清理任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 打印存储统计信息
     * 每小时执行一次
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void printStorageStats() {
        try {
            PhotoService.StorageStats stats = photoService.getStorageStats();
            logger.info("存储统计 - 文件总数: {}, 总大小: {} bytes, 用户数: {}", 
                    stats.getTotalFiles(), stats.getTotalSize(), stats.getTotalUsers());
                    
        } catch (Exception e) {
            logger.error("获取存储统计失败: {}", e.getMessage(), e);
        }
    }
}
