package com.example.photoupload;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 照片上传系统主启动类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@SpringBootApplication
@EnableCaching
@EnableAsync
@EnableScheduling
public class PhotoUploadSystemApplication {

    public static void main(String[] args) {
        SpringApplication.run(PhotoUploadSystemApplication.class, args);
    }
}
