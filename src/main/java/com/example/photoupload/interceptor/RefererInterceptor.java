package com.example.photoupload.interceptor;

import com.example.photoupload.config.SecurityConfig;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import java.net.URL;
import java.util.List;

/**
 * 防盗链拦截器
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Component
public class RefererInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(RefererInterceptor.class);

    @Autowired
    private SecurityConfig securityConfig;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 检查是否启用防盗链
        if (!securityConfig.getReferer().isEnabled()) {
            return true;
        }

        // 只对下载和预览请求进行防盗链检查
        String requestURI = request.getRequestURI();
        if (!requestURI.contains("/download/") && !requestURI.contains("/preview/")) {
            return true;
        }

        String referer = request.getHeader("Referer");
        
        // 如果没有Referer头，允许直接访问（可根据需要调整策略）
        if (!StringUtils.hasText(referer)) {
            logger.debug("请求没有Referer头，允许访问: {}", requestURI);
            return true;
        }

        try {
            URL refererUrl = new URL(referer);
            String refererHost = refererUrl.getHost();
            
            List<String> allowedDomains = securityConfig.getReferer().getAllowedDomains();
            
            // 检查Referer域名是否在允许列表中
            boolean isAllowed = allowedDomains.stream()
                    .anyMatch(domain -> refererHost.equals(domain) || refererHost.endsWith("." + domain));
            
            if (!isAllowed) {
                logger.warn("防盗链检查失败，拒绝访问。Referer: {}, URI: {}", referer, requestURI);
                response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                response.getWriter().write("Access denied: Invalid referer");
                return false;
            }
            
            logger.debug("防盗链检查通过，Referer: {}", referer);
            return true;
            
        } catch (Exception e) {
            logger.error("防盗链检查异常: {}", e.getMessage(), e);
            // 异常情况下允许访问，避免影响正常功能
            return true;
        }
    }
}
