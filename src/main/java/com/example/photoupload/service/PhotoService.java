package com.example.photoupload.service;

import com.example.photoupload.dto.PageResponse;
import com.example.photoupload.dto.PhotoListResponse;
import com.example.photoupload.dto.PhotoUploadResponse;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 照片服务接口
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface PhotoService {

    /**
     * 单文件上传
     * 
     * @param file 上传的文件
     * @param username 上传用户
     * @return 上传结果
     */
    PhotoUploadResponse uploadSingle(MultipartFile file, String username);

    /**
     * 多文件上传
     * 
     * @param files 上传的文件列表
     * @param username 上传用户
     * @return 上传结果列表
     */
    List<PhotoUploadResponse> uploadMultiple(MultipartFile[] files, String username);

    /**
     * 下载文件
     * 
     * @param storedName 存储文件名
     * @return 文件资源
     */
    Resource downloadFile(String storedName);

    /**
     * 预览文件
     * 
     * @param storedName 存储文件名
     * @return 文件资源
     */
    Resource previewFile(String storedName);

    /**
     * 获取缩略图
     * 
     * @param storedName 存储文件名
     * @return 缩略图资源
     */
    Resource getThumbnail(String storedName);

    /**
     * 获取照片列表（分页）
     * 
     * @param pageable 分页参数
     * @return 分页结果
     */
    PageResponse<PhotoListResponse> getPhotoList(Pageable pageable);

    /**
     * 获取用户照片列表（分页）
     * 
     * @param username 用户名
     * @param pageable 分页参数
     * @return 分页结果
     */
    PageResponse<PhotoListResponse> getUserPhotoList(String username, Pageable pageable);

    /**
     * 删除照片
     * 
     * @param id 照片ID
     * @param username 操作用户
     * @return 是否删除成功
     */
    boolean deletePhoto(Long id, String username);

    /**
     * 批量删除照片
     * 
     * @param ids 照片ID列表
     * @param username 操作用户
     * @return 删除成功的数量
     */
    int batchDeletePhotos(List<Long> ids, String username);

    /**
     * 获取照片详情
     * 
     * @param id 照片ID
     * @return 照片详情
     */
    PhotoListResponse getPhotoDetail(Long id);

    /**
     * 检查文件是否存在
     * 
     * @param storedName 存储文件名
     * @return 是否存在
     */
    boolean fileExists(String storedName);

    /**
     * 获取文件内容类型
     * 
     * @param storedName 存储文件名
     * @return 内容类型
     */
    String getContentType(String storedName);

    /**
     * 清理过期文件
     * 
     * @param days 过期天数
     * @return 清理的文件数量
     */
    int cleanupExpiredFiles(int days);

    /**
     * 获取存储统计信息
     * 
     * @return 统计信息
     */
    StorageStats getStorageStats();

    /**
     * 存储统计信息内部类
     */
    class StorageStats {
        private long totalFiles;
        private long totalSize;
        private long totalUsers;

        public StorageStats(long totalFiles, long totalSize, long totalUsers) {
            this.totalFiles = totalFiles;
            this.totalSize = totalSize;
            this.totalUsers = totalUsers;
        }

        // Getters
        public long getTotalFiles() {
            return totalFiles;
        }

        public long getTotalSize() {
            return totalSize;
        }

        public long getTotalUsers() {
            return totalUsers;
        }
    }
}
