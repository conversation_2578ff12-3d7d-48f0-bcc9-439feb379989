package com.example.photoupload.service.impl;

import com.example.photoupload.config.FileUploadConfig;
import com.example.photoupload.dto.PageResponse;
import com.example.photoupload.dto.PhotoListResponse;
import com.example.photoupload.dto.PhotoUploadResponse;
import com.example.photoupload.entity.PhotoInfo;
import com.example.photoupload.exception.FileProcessingException;
import com.example.photoupload.exception.FileValidationException;
import com.example.photoupload.repository.PhotoInfoRepository;
import com.example.photoupload.service.PhotoService;
import com.example.photoupload.util.FileUtil;
import com.example.photoupload.util.ImageUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 照片服务实现类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional
public class PhotoServiceImpl implements PhotoService {

    private static final Logger logger = LoggerFactory.getLogger(PhotoServiceImpl.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private PhotoInfoRepository photoInfoRepository;

    @Autowired
    private FileUploadConfig fileUploadConfig;

    @Autowired
    private FileUtil fileUtil;

    @Autowired
    private ImageUtil imageUtil;

    @Override
    public PhotoUploadResponse uploadSingle(MultipartFile file, String username) {
        logger.info("开始上传单个文件: {}, 用户: {}", file.getOriginalFilename(), username);
        
        try {
            // 验证文件
            validateFile(file);
            
            // 检查文件是否已存在（基于MD5）
            String md5Hash = fileUtil.calculateMD5(file);
            Optional<PhotoInfo> existingPhoto = photoInfoRepository.findByMd5HashAndIsDeletedFalse(md5Hash);
            if (existingPhoto.isPresent()) {
                logger.info("文件已存在，返回现有文件信息: {}", existingPhoto.get().getStoredName());
                return convertToUploadResponse(existingPhoto.get());
            }
            
            // 生成存储文件名
            String storedName = fileUtil.generateStoredName(file.getOriginalFilename());
            
            // 确保上传目录存在
            Path uploadPath = Paths.get(fileUploadConfig.getPath());
            Files.createDirectories(uploadPath);
            
            // 保存原始文件
            Path filePath = uploadPath.resolve(storedName);
            Files.copy(file.getInputStream(), filePath);
            
            // 创建照片信息
            PhotoInfo photoInfo = new PhotoInfo();
            photoInfo.setOriginalName(file.getOriginalFilename());
            photoInfo.setStoredName(storedName);
            photoInfo.setFilePath(filePath.toString());
            photoInfo.setFileSize(file.getSize());
            photoInfo.setContentType(file.getContentType());
            photoInfo.setMd5Hash(md5Hash);
            photoInfo.setUploadedBy(username);
            
            // 处理图片（获取尺寸、压缩、生成缩略图）
            processImage(file, photoInfo, filePath);
            
            // 保存到数据库
            photoInfo = photoInfoRepository.save(photoInfo);
            
            logger.info("文件上传成功: {}", storedName);
            return convertToUploadResponse(photoInfo);
            
        } catch (Exception e) {
            logger.error("文件上传失败: {}", e.getMessage(), e);
            throw new FileProcessingException("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public List<PhotoUploadResponse> uploadMultiple(MultipartFile[] files, String username) {
        logger.info("开始上传多个文件，数量: {}, 用户: {}", files.length, username);
        
        List<PhotoUploadResponse> responses = new ArrayList<>();
        for (MultipartFile file : files) {
            try {
                PhotoUploadResponse response = uploadSingle(file, username);
                responses.add(response);
            } catch (Exception e) {
                logger.error("文件上传失败: {}, 错误: {}", file.getOriginalFilename(), e.getMessage());
                // 继续处理其他文件
            }
        }
        
        logger.info("多文件上传完成，成功: {}/{}", responses.size(), files.length);
        return responses;
    }

    @Override
    @Transactional(readOnly = true)
    public Resource downloadFile(String storedName) {
        logger.debug("下载文件: {}", storedName);
        
        Optional<PhotoInfo> photoInfo = photoInfoRepository.findByStoredNameAndIsDeletedFalse(storedName);
        if (photoInfo.isEmpty()) {
            throw new FileProcessingException("文件不存在: " + storedName);
        }
        
        try {
            Path filePath = Paths.get(photoInfo.get().getFilePath());
            Resource resource = new UrlResource(filePath.toUri());
            
            if (resource.exists() && resource.isReadable()) {
                // 更新访问统计
                updateAccessStats(photoInfo.get().getId());
                return resource;
            } else {
                throw new FileProcessingException("文件不可读: " + storedName);
            }
        } catch (Exception e) {
            logger.error("文件下载失败: {}", e.getMessage(), e);
            throw new FileProcessingException("文件下载失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Resource previewFile(String storedName) {
        // 预览功能与下载相同，但可以添加额外的预览逻辑
        return downloadFile(storedName);
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "thumbnails", key = "#storedName")
    public Resource getThumbnail(String storedName) {
        logger.debug("获取缩略图: {}", storedName);
        
        Optional<PhotoInfo> photoInfo = photoInfoRepository.findByStoredNameAndIsDeletedFalse(storedName);
        if (photoInfo.isEmpty()) {
            throw new FileProcessingException("文件不存在: " + storedName);
        }
        
        try {
            String thumbnailPath = photoInfo.get().getThumbnailPath();
            if (thumbnailPath != null) {
                Path filePath = Paths.get(thumbnailPath);
                Resource resource = new UrlResource(filePath.toUri());
                
                if (resource.exists() && resource.isReadable()) {
                    return resource;
                }
            }
            
            // 如果缩略图不存在，返回原图
            return downloadFile(storedName);
            
        } catch (Exception e) {
            logger.error("获取缩略图失败: {}", e.getMessage(), e);
            throw new FileProcessingException("获取缩略图失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public PageResponse<PhotoListResponse> getPhotoList(Pageable pageable) {
        logger.debug("获取照片列表，页码: {}, 大小: {}", pageable.getPageNumber(), pageable.getPageSize());
        
        Page<PhotoInfo> page = photoInfoRepository.findByIsDeletedFalseOrderByUploadTimeDesc(pageable);
        List<PhotoListResponse> content = page.getContent().stream()
                .map(this::convertToListResponse)
                .toList();
        
        return new PageResponse<>(content, page.getNumber(), page.getSize(), 
                page.getTotalElements(), page.getTotalPages());
    }

    @Override
    @Transactional(readOnly = true)
    public PageResponse<PhotoListResponse> getUserPhotoList(String username, Pageable pageable) {
        logger.debug("获取用户照片列表，用户: {}, 页码: {}, 大小: {}", 
                username, pageable.getPageNumber(), pageable.getPageSize());
        
        Page<PhotoInfo> page = photoInfoRepository.findByUploadedByAndIsDeletedFalseOrderByUploadTimeDesc(
                username, pageable);
        List<PhotoListResponse> content = page.getContent().stream()
                .map(this::convertToListResponse)
                .toList();
        
        return new PageResponse<>(content, page.getNumber(), page.getSize(), 
                page.getTotalElements(), page.getTotalPages());
    }

    /**
     * 验证上传文件
     */
    private void validateFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new FileValidationException("文件不能为空");
        }
        
        if (file.getSize() > fileUploadConfig.getMaxSize()) {
            throw new FileValidationException("文件大小超过限制: " + fileUploadConfig.getMaxSize() + " bytes");
        }
        
        String contentType = file.getContentType();
        if (contentType == null || !fileUploadConfig.getAllowedTypes().contains(contentType)) {
            throw new FileValidationException("不支持的文件类型: " + contentType);
        }
        
        // 使用Tika进行更严格的文件类型检查
        if (!fileUtil.isValidImageFile(file)) {
            throw new FileValidationException("文件内容与扩展名不匹配");
        }
    }

    /**
     * 处理图片（压缩、生成缩略图等）
     */
    private void processImage(MultipartFile file, PhotoInfo photoInfo, Path filePath) throws IOException {
        // 获取图片尺寸
        int[] dimensions = imageUtil.getImageDimensions(file);
        if (dimensions != null) {
            photoInfo.setWidth(dimensions[0]);
            photoInfo.setHeight(dimensions[1]);
        }
        
        // 压缩图片
        if (fileUploadConfig.getCompression().isEnabled()) {
            boolean compressed = imageUtil.compressImage(filePath, 
                    fileUploadConfig.getCompression().getQuality(),
                    fileUploadConfig.getCompression().getMaxWidth(),
                    fileUploadConfig.getCompression().getMaxHeight());
            photoInfo.setIsCompressed(compressed);
        }
        
        // 生成缩略图
        String thumbnailPath = imageUtil.generateThumbnail(filePath,
                fileUploadConfig.getThumbnail().getWidth(),
                fileUploadConfig.getThumbnail().getHeight(),
                fileUploadConfig.getThumbnail().getQuality());
        photoInfo.setThumbnailPath(thumbnailPath);
    }

    /**
     * 更新访问统计
     */
    private void updateAccessStats(Long photoId) {
        try {
            photoInfoRepository.updateAccessStats(photoId, LocalDateTime.now());
        } catch (Exception e) {
            logger.warn("更新访问统计失败: {}", e.getMessage());
        }
    }

    /**
     * 转换为上传响应DTO
     */
    private PhotoUploadResponse convertToUploadResponse(PhotoInfo photoInfo) {
        PhotoUploadResponse response = new PhotoUploadResponse();
        response.setId(photoInfo.getId());
        response.setOriginalName(photoInfo.getOriginalName());
        response.setStoredName(photoInfo.getStoredName());
        response.setFileSize(photoInfo.getFileSize());
        response.setContentType(photoInfo.getContentType());
        response.setWidth(photoInfo.getWidth());
        response.setHeight(photoInfo.getHeight());
        response.setIsCompressed(photoInfo.getIsCompressed());
        response.setUploadTime(photoInfo.getUploadTime().format(DATE_FORMATTER));
        response.setMd5Hash(photoInfo.getMd5Hash());
        
        // 生成URL
        response.setDownloadUrl("/api/photos/download/" + photoInfo.getStoredName());
        response.setPreviewUrl("/api/photos/preview/" + photoInfo.getStoredName());
        if (photoInfo.getThumbnailPath() != null) {
            response.setThumbnailUrl("/api/photos/thumbnail/" + photoInfo.getStoredName());
        }
        
        return response;
    }

    @Override
    public boolean deletePhoto(Long id, String username) {
        logger.info("删除照片: {}, 用户: {}", id, username);

        Optional<PhotoInfo> photoInfo = photoInfoRepository.findById(id);
        if (photoInfo.isEmpty()) {
            logger.warn("照片不存在: {}", id);
            return false;
        }

        PhotoInfo photo = photoInfo.get();
        // 检查权限（管理员可以删除所有照片，普通用户只能删除自己的照片）
        if (!"admin".equals(username) && !username.equals(photo.getUploadedBy())) {
            logger.warn("用户 {} 无权删除照片 {}", username, id);
            return false;
        }

        // 软删除
        int result = photoInfoRepository.softDeleteById(id);
        if (result > 0) {
            logger.info("照片删除成功: {}", id);
            return true;
        }

        return false;
    }

    @Override
    public int batchDeletePhotos(List<Long> ids, String username) {
        logger.info("批量删除照片: {}, 用户: {}", ids, username);

        List<Long> validIds = new ArrayList<>();
        for (Long id : ids) {
            Optional<PhotoInfo> photoInfo = photoInfoRepository.findById(id);
            if (photoInfo.isPresent()) {
                PhotoInfo photo = photoInfo.get();
                // 检查权限
                if ("admin".equals(username) || username.equals(photo.getUploadedBy())) {
                    validIds.add(id);
                }
            }
        }

        if (!validIds.isEmpty()) {
            int result = photoInfoRepository.softDeleteByIds(validIds);
            logger.info("批量删除完成，成功删除: {}/{}", result, ids.size());
            return result;
        }

        return 0;
    }

    @Override
    @Transactional(readOnly = true)
    public PhotoListResponse getPhotoDetail(Long id) {
        logger.debug("获取照片详情: {}", id);

        Optional<PhotoInfo> photoInfo = photoInfoRepository.findById(id);
        if (photoInfo.isEmpty() || photoInfo.get().getIsDeleted()) {
            throw new FileProcessingException("照片不存在: " + id);
        }

        return convertToListResponse(photoInfo.get());
    }

    @Override
    @Transactional(readOnly = true)
    public boolean fileExists(String storedName) {
        Optional<PhotoInfo> photoInfo = photoInfoRepository.findByStoredNameAndIsDeletedFalse(storedName);
        if (photoInfo.isEmpty()) {
            return false;
        }

        Path filePath = Paths.get(photoInfo.get().getFilePath());
        return Files.exists(filePath);
    }

    @Override
    @Transactional(readOnly = true)
    public String getContentType(String storedName) {
        Optional<PhotoInfo> photoInfo = photoInfoRepository.findByStoredNameAndIsDeletedFalse(storedName);
        return photoInfo.map(PhotoInfo::getContentType).orElse(null);
    }

    @Override
    public int cleanupExpiredFiles(int days) {
        logger.info("开始清理 {} 天前的过期文件", days);

        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(days);
        List<PhotoInfo> expiredPhotos = photoInfoRepository.findUnusedPhotosBeforeTime(cutoffTime);

        int cleanedCount = 0;
        for (PhotoInfo photo : expiredPhotos) {
            try {
                // 删除物理文件
                Path filePath = Paths.get(photo.getFilePath());
                if (Files.exists(filePath)) {
                    Files.delete(filePath);
                }

                // 删除缩略图
                if (photo.getThumbnailPath() != null) {
                    Path thumbnailPath = Paths.get(photo.getThumbnailPath());
                    if (Files.exists(thumbnailPath)) {
                        Files.delete(thumbnailPath);
                    }
                }

                // 软删除数据库记录
                photoInfoRepository.softDeleteById(photo.getId());
                cleanedCount++;

            } catch (Exception e) {
                logger.error("清理文件失败: {}, 错误: {}", photo.getStoredName(), e.getMessage());
            }
        }

        logger.info("清理完成，删除文件数量: {}", cleanedCount);
        return cleanedCount;
    }

    @Override
    @Transactional(readOnly = true)
    public StorageStats getStorageStats() {
        long totalFiles = photoInfoRepository.countByIsDeletedFalse();
        Long totalSize = photoInfoRepository.sumFileSizeByIsDeletedFalse();
        if (totalSize == null) {
            totalSize = 0L;
        }

        // 统计用户数量（简化实现）
        long totalUsers = photoInfoRepository.findByIsDeletedFalseOrderByUploadTimeDesc(
                org.springframework.data.domain.PageRequest.of(0, Integer.MAX_VALUE))
                .getContent().stream()
                .map(PhotoInfo::getUploadedBy)
                .distinct()
                .count();

        return new StorageStats(totalFiles, totalSize, totalUsers);
    }

    /**
     * 转换为列表响应DTO
     */
    private PhotoListResponse convertToListResponse(PhotoInfo photoInfo) {
        PhotoListResponse response = new PhotoListResponse();
        response.setId(photoInfo.getId());
        response.setOriginalName(photoInfo.getOriginalName());
        response.setStoredName(photoInfo.getStoredName());
        response.setFileSize(photoInfo.getFileSize());
        response.setContentType(photoInfo.getContentType());
        response.setWidth(photoInfo.getWidth());
        response.setHeight(photoInfo.getHeight());
        response.setUploadTime(photoInfo.getUploadTime().format(DATE_FORMATTER));
        response.setAccessCount(photoInfo.getAccessCount());
        response.setUploadedBy(photoInfo.getUploadedBy());

        if (photoInfo.getLastAccessTime() != null) {
            response.setLastAccessTime(photoInfo.getLastAccessTime().format(DATE_FORMATTER));
        }

        // 生成URL
        response.setDownloadUrl("/api/photos/download/" + photoInfo.getStoredName());
        response.setPreviewUrl("/api/photos/preview/" + photoInfo.getStoredName());
        if (photoInfo.getThumbnailPath() != null) {
            response.setThumbnailUrl("/api/photos/thumbnail/" + photoInfo.getStoredName());
        }

        return response;
    }
}
