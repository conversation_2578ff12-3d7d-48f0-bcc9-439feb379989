package com.example.photoupload.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 安全配置类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Configuration
@ConfigurationProperties(prefix = "security")
public class SecurityConfig {

    /**
     * 防盗链配置
     */
    private Referer referer = new Referer();

    /**
     * 访问令牌配置
     */
    private Token token = new Token();

    public Referer getReferer() {
        return referer;
    }

    public void setReferer(Referer referer) {
        this.referer = referer;
    }

    public Token getToken() {
        return token;
    }

    public void setToken(Token token) {
        this.token = token;
    }

    /**
     * 防盗链配置内部类
     */
    public static class Referer {
        private boolean enabled = true;
        private List<String> allowedDomains;

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public List<String> getAllowedDomains() {
            return allowedDomains;
        }

        public void setAllowedDomains(List<String> allowedDomains) {
            this.allowedDomains = allowedDomains;
        }
    }

    /**
     * 访问令牌配置内部类
     */
    public static class Token {
        private String secret = "mySecretKey123456789";
        private long expiration = 3600000; // 1小时

        public String getSecret() {
            return secret;
        }

        public void setSecret(String secret) {
            this.secret = secret;
        }

        public long getExpiration() {
            return expiration;
        }

        public void setExpiration(long expiration) {
            this.expiration = expiration;
        }
    }
}
