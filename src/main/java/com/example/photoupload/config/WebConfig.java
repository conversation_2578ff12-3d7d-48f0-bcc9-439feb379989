package com.example.photoupload.config;

import com.example.photoupload.interceptor.RefererInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private RefererInterceptor refererInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加防盗链拦截器
        registry.addInterceptor(refererInterceptor)
                .addPathPatterns("/api/photos/download/**", "/api/photos/preview/**")
                .excludePathPatterns("/api/photos/thumbnail/**"); // 缩略图不进行防盗链检查
    }
}
