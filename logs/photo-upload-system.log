2025-07-11 17:44:08 [main] INFO  c.e.p.PhotoUploadSystemApplication - Starting PhotoUploadSystemApplication using Java 21.0.1 with PID 48541 (/Users/<USER>/workspace/remote_agent_java_test/test_photo_java/target/classes started by ecchanger in /Users/<USER>/workspace/remote_agent_java_test/test_photo_java)
2025-07-11 17:44:08 [main] DEBUG c.e.p.PhotoUploadSystemApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-07-11 17:44:08 [main] INFO  c.e.p.PhotoUploadSystemApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-11 17:44:08 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11 17:44:09 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 1 JPA repository interface.
2025-07-11 17:44:09 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-07-11 17:44:09 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-11 17:44:09 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-07-11 17:44:09 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-11 17:44:09 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1044 ms
2025-07-11 17:44:09 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 17:44:09 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection conn0: url=jdbc:h2:mem:testdb user=SA
2025-07-11 17:44:09 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 17:44:09 [main] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
2025-07-11 17:44:09 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11 17:44:09 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.3.1.Final
2025-07-11 17:44:09 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-11 17:44:10 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-11 17:44:10 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-11 17:44:10 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-11 17:44:10 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 17:44:10 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-11 17:44:11 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-11 17:44:11 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-11 17:44:11 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-11 17:44:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@30b0d271, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7e4fc391, org.springframework.security.web.context.SecurityContextHolderFilter@766f2d65, org.springframework.security.web.header.HeaderWriterFilter@c0d114b, org.springframework.web.filter.CorsFilter@11869005, org.springframework.security.web.authentication.logout.LogoutFilter@41194707, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@a95807f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5841716b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5e5d41dc, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4589bc66, org.springframework.security.web.access.ExceptionTranslationFilter@631f188a, org.springframework.security.web.access.intercept.AuthorizationFilter@8d0d52a]
2025-07-11 17:44:11 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8081 (http) with context path ''
2025-07-11 17:44:11 [main] INFO  c.e.p.PhotoUploadSystemApplication - Started PhotoUploadSystemApplication in 3.582 seconds (process running for 4.461)
