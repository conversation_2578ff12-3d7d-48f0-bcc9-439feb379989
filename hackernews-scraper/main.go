package main

import (
	"fmt"
	"log"
	"net/http"

	"golang.org/x/net/html"
)

func main() {
	fmt.Println("Hacker News Scraper")

	resp, err := http.Get("https://news.ycombinator.com/")
	if err != nil {
		log.Fatalf("failed to get Hacker News: %s", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Fatalf("failed to get Hacker News: status code %d", resp.StatusCode)
	}

	doc, err := html.Parse(resp.Body)
	if err != nil {
		log.Fatalf("failed to parse HTML: %s", err)
	}

	var f func(*html.Node)
	f = func(n *html.Node) {
		if n.Type == html.ElementNode && n.Data == "span" {
			for _, a := range n.Attr {
				if a.Key == "class" && a.Val == "titleline" {
					// Found a titleline span. Now find the 'a' tag inside.
					for c := n.FirstChild; c != nil; c = c.NextSibling {
						if c.Type == html.ElementNode && c.Data == "a" {
							title := c.FirstChild.Data
							var url string
							for _, attr := range c.Attr {
								if attr.Key == "href" {
									url = attr.Val
									break
								}
							}
							fmt.Printf("Title: %s\n", title)
							fmt.Printf("URL: %s\n\n", url)
							// We only care about the first 'a' tag.
							break
						}
					}
				}
			}
		}
		for c := n.FirstChild; c != nil; c = c.NextSibling {
			f(c)
		}
	}
	f(doc)
}